import React from 'react';
import { FaFacebook, FaTwitter, FaInstagram, FaSignInAlt, FaTiktok } from 'react-icons/fa';
import './navbar.css'; 

const CustomNavbar = () => {
  return (
    <div className="custom-navbar">
      <div className="navbar-logo">
        <img src="/images/logo.png" alt="Company Logo"  />
      </div>
      
      <div className="social-icons">
        <a href="https://facebook.com" aria-label="Facebook"><FaFacebook /></a>
        <a href="https://twitter.com" aria-label="Tiktok"><FaTiktok /></a>
        <a href="https://instagram.com" aria-label="Instagram"><FaInstagram /></a>
      </div>
      
      <div className="navbar-text">
        <b><p>Save your favourite restaurants and deals.</p></b>
      
      <button className="sign-in-btn">Sign In</button>
      </div>
      
      <div className="navbar-divider"></div>
      
      <nav className="navbar-options">
        <b><a href="/explrDeals">Explore Deals</a></b>
        <a href="/explrRes">Explore Restaurants</a>
        <a href="/blg">Blog</a>
        <a href="/abt">About Dine Deals</a>
        <div className="navbar-divider"></div>

        <a href="/sgst">Suggest a Restaurant</a>

        <div className="navbar-divider"></div>

        <button className="sign-in-btn">
        <FaSignInAlt /> Restaurant Sign In
      </button>

        <div className="navbar-divider"></div>

        <div className="navbar-bottom">
        <a href="" aria-label="">Contact Us</a>
        <a href="" aria-label="">Terms & Conditions</a>
        <a href="" aria-label="">Privacy Policy</a>
      </div>

      </nav>

      
    </div>
  );
};

export default CustomNavbar;
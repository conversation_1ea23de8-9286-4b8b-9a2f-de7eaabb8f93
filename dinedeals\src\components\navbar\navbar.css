.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 180px; 
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto; 
}
  
  .navbar-logo {
    margin-bottom: 15px;
    align-self: center;
  }
  
  .social-icons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15px;
  }
  
  .social-icons a {
    color: #333;
    font-size: 1.2rem;
    transition: color 0.3s;
  }
  
  .social-icons a:hover {
    color: #4267B2; 
  }
  
  .navbar-text {
    margin-bottom: 10px;
    font-size: 0.8rem;
    color: #000000;
  }
  
  .sign-in-btn {
    background-color: #ed892b;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 15px;
    transition: background-color 0.3s;
    font-size: 0.7rem;
    font-weight: bold;
  }
  
  .sign-in-btn:hover {
    background-color: #365899;
  }
  
  .sign-in-btn svg {
    margin-right: 5px;
  }
  
  .navbar-divider {
    width: 100%;
    height: 1px;
    background-color: #ddd;
    margin: 10px 0;
  }
  
  .navbar-options {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .navbar-options a {
    padding: 8px 0;
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
    font-size: 0.6rem;
  }
  
  .navbar-options a:hover {
    color: #4267B2;
  }

  .navbar-bottom {
    margin-top: auto;
    display: flex;
    flex-direction: column;
  }
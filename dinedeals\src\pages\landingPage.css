
* {
    box-sizing: border-box;
    
  }

  html, body {
    margin: 0;
    padding: 0;
    height: auto;
    overflow-x: hidden;
    overflow-y: auto;
  }
  
  

  
  .landing-container {
    font-family: 'Arial Rounded MT Bold', 'Segoe UI', sans-serif;
    color: #3a0e00;
    display: flex;
    justify-content: space-between;
    background-color: #fef1d6;
    width: 100%;
    align-items: center;
    padding: 60px 80px;
    height: auto; 
    position: relative;
    background-color: #fef1d6;
  }
  
  .left-section {
    flex: 1;
    max-width: 50%;
    padding-right: 20px;
  }
  
  .logo-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
  }
  
  .logo {
    width: 50px;
    margin-right: 10px;
  }
  
  .brand-name {
    font-size: 1.8rem;
    font-weight: bold;
  }
  
  .main-heading {
    font-size: 3rem;
    font-weight: 800;
    color: #872d1e;
    line-height: 1.2;
    margin-bottom: 30px;
  }
  
  .subheading,
  .description {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 15px;
  }
  
  .right-section {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-end;

    height: 100%;
  }
  
  .main-image {
    height: 100%;
    object-fit: contain;
    max-width: 100%;
    z-index: 1;
  }







.next-section {
    padding: 30px 80px;
    text-align: center;
    background-color: white;
  }
  
  .button-group {
    margin-top: 0px;
    display: flex;
    justify-content: center;
    gap: 20px;
  }
  
  .nav-button {
    display: flex;                
  justify-content: center;     
  align-items: center; 
    text-decoration: none;
    background-color: #A6E1E9;
    width: 500px;
    height: 90px;
    color: #084b54;
    padding: 16px 32px;
    border-radius: 35px;
    font-size: 1.3rem;
    font-weight: bold;
    transition: background-color 0.3s ease;
  }
  
  .nav-button:hover {
    background-color: #afdce1;
  }
  
  .nav-button.secondary {
    background-color: #A08FDC;
    color: #2e0c7c;
  }
  
  .nav-button.secondary:hover {
    background-color: #c6bee2;
  }


  .why-section {
    padding: 60px 20px;
    text-align: center;
    background-color: #fff;
  }
  
  .main-heading {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2d2d2d;
  }
  
  .subtext {
    font-size: 1.1rem;
    color: #666;
    margin-top: 10px;
  }
  
  .card-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    margin-top: 40px;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
  }
  
  @media (min-width: 768px) {
    .card-container {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  .info-card {
    background-color: #fff;
    padding: 24px;
    border-radius: 16px;
    border: 1px solid #f0f0f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(93, 93, 255, 0.15);
  }
  
  .icon {
    font-size: 2.5rem;
    color: #fc7540;
    margin-bottom: 16px;
  }
  
  .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }
  
  .card-description {
    font-size: 0.95rem;
    color: #666;
  }
  
  
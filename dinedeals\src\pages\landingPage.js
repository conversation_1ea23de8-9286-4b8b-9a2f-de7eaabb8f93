import React from 'react';
import './landingPage.css';
import { FaUtensils, FaTags, FaSmile, FaDollarSign, FaUtensilSpoon, FaFonticonsFi, FaBell } from "react-icons/fa";


export default function LandingPage() {
  return (
    <div className="landing">
        <div className="landing-container">
      <div className="left-section">
        <div className="logo-title">
          <img src="./images/logo.png" alt="Dinin Deals Logo" className="logo" />
          <h1 className="brand-name">Dinin Deals</h1>
        </div>
        <h2 className="main-heading">
          Tired of missing out<br />on restaurant deals?
        </h2>
        <p className="subheading">
          Every day, local restaurants offer amazing specials—but most people never hear about them.
        </p>
        <p className="description">
          Dinin Deals makes it easy to discover nearby discounts, daily specials, and happy hours, all in one place.
        </p>
      </div>
      <div className="right-section">
        <img src="./images/heroGirl.png" alt="Woman with Phone" className="main-image" />
      </div>
    </div>

{/* Allah e janey keno hoy na ei baal  */}


    <div className="next-section" id="next-section">
  <div className="button-group">
    <a href="#form1" className="nav-button">List your Restaurant</a>
    <a href="#form2" className="nav-button secondary">Signup for early access</a>
  </div>
</div>


<div className="why-section">
  <h1 className="main-heading">Why Dinin Deals?</h1>
  <p className="subtext">
    Discover why thousands trust us to enhance their dining experiences.
  </p>

  <div className="card-container">
    <div className="info-card">
      <div className="card-icon">
        <FaDollarSign className="icon" />
      </div>
      <h2 className="card-title">Browse Exclusive Discounts</h2>
      <p className="card-description">
Find the best happy hours, flash deals and hidden gem offers near you.      </p>
    </div>

    <div className="info-card">
      <div className="card-icon">
        <FaUtensils className="icon" />
      </div>
      <h2 className="card-title">Never miss a deal</h2>
      <p className="card-description">
        Get notified the moment your favourite spot post new specials and offers.
      </p>
    </div>

    <div className="info-card">
      <div className="card-icon">
        <FaBell className="icon" />
      </div>
      <h2 className="card-title">Customer Satisfaction</h2>
      <p className="card-description">
        Your happiness is our priority. We guarantee a delightful dining journey.
      </p>
    </div>
  </div>
</div>





    </div>
  );
}
